// ==UserScript==
// @name         Facebook Auto Liker
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  自動為指定的 Facebook 貼文點讚
// <AUTHOR> Name
// @match        https://www.facebook.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 存储当前状态到 localStorage
    function saveCurrentState(isRunning, originalUrl = '', finalUrl = '') {
        localStorage.setItem('fb_auto_like_state', JSON.stringify({
            isRunning: isRunning,
            originalUrl: originalUrl,
            finalUrl: finalUrl,
            timestamp: new Date().getTime()
        }));
    }

    // 从 localStorage 获取状态
    function getStoredState() {
        const state = localStorage.getItem('fb_auto_like_state');
        if (state) {
            const parsedState = JSON.parse(state);
            if (new Date().getTime() - parsedState.timestamp < 300000) {
                return parsedState;
            }
        }
        return { isRunning: false, originalUrl: '', finalUrl: '' };
    }

    // 初始化运行状态
    const storedState = getStoredState();
    let isRunning = storedState.isRunning;
    let currentOriginalUrl = storedState.originalUrl;
    let currentFinalUrl = storedState.finalUrl;

    // 設定要自動點讚的貼文 URL 列表
    const targetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts') || '[]');
    // 已點讚的貼文記錄
    const likedPosts = JSON.parse(localStorage.getItem('fb_liked_posts') || '[]');

    // 獲取待點讚的貼文列表
    function getPendingPosts() {
        return targetPosts.filter(url => !likedPosts.includes(url));
    }

    // 清除所有待处理链接
    function clearAllPendingLinks() {
        targetPosts.length = 0;
        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
        logUserAction('清除所有待处理链接');
        updatePendingList();
    }

    // 用户操作日志记录函数
    function logUserAction(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);

        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                padding: 5px 0;
                border-bottom: 1px solid #eee;
                font-size: 12px;
            `;
            logEntry.innerHTML = `<span style="color:#999;">[${timestamp}]</span> ${message}`;
            logPanel.insertBefore(logEntry, logPanel.firstChild);

            // 限制日志条目数量
            if (logPanel.children.length > 50) {
                logPanel.removeChild(logPanel.lastChild);
            }
        }
    }

    // 清除日志
    function clearLogs() {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            logPanel.innerHTML = '';
        }
    }

    // 创建按钮
    function createButton(text, bgColor, hoverColor) {
        const button = document.createElement('button');
        button.textContent = text;

        button.style.cssText = `
            background: ${bgColor};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        `;
        button.onmouseover = () => button.style.background = hoverColor;
        button.onmouseout = () => button.style.background = bgColor;
        return button;
    }

    // 显示通知
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : '#F44336'};
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s';
            setTimeout(() => document.body.removeChild(toast), 500);
        }, 3000);
    }

    // 解析Facebook链接
    function parseFacebookUrls(text) {
        const urlRegex = /(https?:\/\/(?:www\.|m\.)?facebook\.com\/[^\s]+)/g;
        const matches = text.match(urlRegex);
        return matches ? matches : [];
    }

    // 添加到点赞列表
    function addToLikeList(input) {
        if (!input) return false;

        const urls = parseFacebookUrls(input);
        if (urls.length === 0) {
            alert('未找到有效的 Facebook 链接！');
            return false;
        }

        let addedCount = 0;
        urls.forEach(url => {
            if (!targetPosts.includes(url)) {
                targetPosts.push(url);
                addedCount++;
            }
        });

        if (addedCount > 0) {
            localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
            logUserAction(`批量添加 ${addedCount} 个链接到待点赞列表`);
            return true;
        }

        alert('所有链接都已经在列表中！');
        return false;
    }

    // 标记已点赞
    function markAsLiked(url) {
        if (!likedPosts.includes(url)) {
            likedPosts.push(url);
            localStorage.setItem('fb_liked_posts', JSON.stringify(likedPosts));
            logUserAction(`完成点赞并标记: ${url}`);
        }
    }

    // 检查是否已经点赞成功
    function checkLikeSuccess() {
        // 查找"移除赞"按钮
        const removeLikeButton = document.querySelector('[aria-label="移除赞"]');

        // 检查是否有蓝色高亮的点赞按钮（另一种已点赞的指示）
        const highlightedLikeButton = document.querySelector('[aria-label="赞"][role="button"][class*="active"]');

        // 检查是否有其他可能的点赞成功指示（如带有特定样式的点赞图标）
        const likeIcon = document.querySelector('svg[aria-label="赞"][fill="#1877F2"]');

        const result = !!removeLikeButton || !!highlightedLikeButton || !!likeIcon;
        console.log('点赞状态检查:', result ? '已点赞' : '未点赞', {
            removeLikeButton: !!removeLikeButton,
            highlightedLikeButton: !!highlightedLikeButton,
            likeIcon: !!likeIcon
        });
        return result;
    }

    // 查找點讚按鈕
    function findLikeButton() {
        const likeButtons = Array.from(document.querySelectorAll('[aria-label="赞"][role="button"]'));
        console.log('找到点赞按钮数量:', likeButtons.length);

        // 排除 MainFeed 元素内的按钮 (适用于所有格式)
        const mainFeedElement = document.querySelector('[data-pagelet="MainFeed"]');

        if (mainFeedElement && likeButtons.length > 0) {
            // 过滤掉在 MainFeed 元素内的按钮
            const filteredButtons = likeButtons.filter(button => {
                return !mainFeedElement.contains(button);
            });

            console.log('排除 MainFeed 内的按钮后剩余数量:', filteredButtons.length);

            // 使用第一个不在 MainFeed 内的按钮
            if (filteredButtons.length > 0) {
                console.log('使用不在 MainFeed 内的第一个点赞按钮');
                return filteredButtons[0];
            }
        }

        // 如果没有找到合适的按钮，尝试其他选择器
        const alternativeButtons = document.querySelectorAll('[data-testid="UFI2ReactionLink"]');
        if (alternativeButtons.length > 0) {
            console.log('使用替代点赞按钮');
            return alternativeButtons[0];
        }

        // 如果还是没有找到，返回第一个点赞按钮（如果有的话）
        if (likeButtons.length > 0) {
            console.log('使用第一个点赞按钮');
            return likeButtons[0];
        }

        console.log('未找到任何点赞按钮');
        return null;
    }

    // 執行點讚
    async function performLike() {
        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('所有链接已处理完成');
            alert('所有貼文已點讚完成！');
            stopAutoLike();
            return;
        }

        const nextPost = pendingPosts[0];
        logUserAction(`正在处理链接: ${nextPost}`);

        currentOriginalUrl = nextPost;
        currentFinalUrl = '';  // 重置最终URL
        saveCurrentState(true, nextPost, '');

        // 如果当前页面不是目标链接，则跳转
        if (window.location.href !== nextPost) {
            window.location.href = nextPost;
            return;
        }

        // 如果已在目标页面，直接检查点赞状态
        checkAndLike(nextPost);
    }

    // 检查并点赞
    function checkAndLike(originalUrl) {
        // 确保页面已完全加载
        if (document.readyState !== 'complete') {
            setTimeout(() => checkAndLike(originalUrl), 1000);
            return;
        }

        // 记录当前页面URL
        if (!currentFinalUrl) {
            currentFinalUrl = window.location.href;
            saveCurrentState(true, originalUrl, currentFinalUrl);
            logUserAction(`记录最终URL: ${currentFinalUrl}`);
        }

        // 先检查是否已经点赞
        if (checkLikeSuccess()) {
            logUserAction(`检测到已点赞: ${originalUrl}`);
            markAsLiked(originalUrl);
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(true, '', '');
            setTimeout(() => {
                if (isRunning) {
                    performLike();
                }
            }, 2000);
            return;
        }

        // 如果未点赞，开始点赞流程
        let retryCount = 0;
        const maxRetries = 5;
        let likeConfirmed = false;

        const tryLike = setInterval(() => {
            if (!isRunning) {
                clearInterval(tryLike);
                return;
            }

            retryCount++;
            console.log(`尝试点赞 (第 ${retryCount} 次)`);

            // 再次检查是否已点赞成功
            if (checkLikeSuccess()) {
                // 增加额外验证：连续两次检测到点赞成功才确认
                if (likeConfirmed) {
                    clearInterval(tryLike);
                    logUserAction(`点赞成功确认: ${originalUrl}`);
                    markAsLiked(originalUrl);
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 2000);
                    return;
                } else {
                    // 第一次检测到成功，标记但不立即确认
                    likeConfirmed = true;
                    logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                    return;
                }
            } else {
                // 如果检测失败，重置确认标记
                likeConfirmed = false;
            }

            const likeButton = findLikeButton();
            if (likeButton) {
                try {
                    likeButton.click();
                    logUserAction(`点击点赞按钮 (第 ${retryCount} 次)`);

                    // 等待点赞确认
                    setTimeout(() => {
                        if (checkLikeSuccess()) {
                            // 设置初步确认标记
                            likeConfirmed = true;
                            logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                        }
                    }, 1500);
                } catch (error) {
                    logUserAction(`点赞操作出错: ${error.message}`);
                }
            }

            // 如果达到最大重试次数，放弃当前链接
            if (retryCount >= maxRetries) {
                clearInterval(tryLike);
                logUserAction(`达到最大重试次数，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 2000);
            }
        }, 3000); // 每3秒尝试一次

        // 设置总体超时
        setTimeout(() => {
            clearInterval(tryLike);
            if (isRunning && !likeConfirmed) {
                logUserAction(`处理超时，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                performLike();
            }
        }, 30000);
    }

    // 開始自動點讚流程
    function startAutoLike() {
        if (isRunning) {
            logUserAction('自动点赞已在运行中');
            return;
        }

        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('没有待处理的链接');
            alert('没有待点赞的链接！');
            return;
        }

        isRunning = true;
        saveCurrentState(true, '', '');
        logUserAction(`开始自动点赞 - 待处理数量: ${pendingPosts.length}`);
        performLike();
    }

    // 停止自動點讚
    function stopAutoLike() {
        isRunning = false;
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(false, '', '');
        logUserAction('停止自动点赞');
    }

    // 存储公共主页信息
    let publicPages = [];
    let currentPageIndex = 0;
    // 保存的公共主页名称列表（用于标记和循环切换）
    let savedPageNames = JSON.parse(localStorage.getItem('fb_saved_page_names') || '[]');
    // 防止重复切换的标志（仅用于调试版本）
    let isSwitching = false;

    // 获取所有公共主页
    function getAllPublicPages() {
        logUserAction('开始获取所有公共主页');

        // 点击右上角个人主页按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (!profileButton) {
            logUserAction('未找到个人主页按钮');
            return false;
        }

        profileButton.click();
        logUserAction('已点击个人主页按钮');

        // 等待菜单加载并点击"查看所有主页"
        setTimeout(() => {
            const viewAllPagesButton = document.querySelector('[aria-label="查看所有主页"][role="button"]');
            if (!viewAllPagesButton) {
                logUserAction('未找到"查看所有主页"按钮');
                return false;
            }

            viewAllPagesButton.click();
            logUserAction('已点击"查看所有主页"按钮');

            // 等待页面列表加载
            setTimeout(() => {
                // 获取所有公共主页
                const pageItems = document.querySelectorAll('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');

                publicPages = [];
                const currentPageNames = [];

                pageItems.forEach((item, index) => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName) {
                        publicPages.push({
                            name: pageName,
                            element: item,
                            index: index
                        });
                        currentPageNames.push(pageName);
                    }
                });

                // 保存公共主页名称到本地存储做标记
                if (currentPageNames.length > 0) {
                    savedPageNames = currentPageNames;
                    localStorage.setItem('fb_saved_page_names', JSON.stringify(savedPageNames));
                    logUserAction(`已保存 ${savedPageNames.length} 个公共主页名称到本地存储`);
                }

                // 记录找到的公共主页
                const pagesInfo = publicPages.map(p => `${p.name} (索引: ${p.index})`);
                logUserAction(`找到 ${publicPages.length} 个公共主页: ${pagesInfo.join(', ')}`);

                // 关闭菜单
                document.body.click();

                showToast(`找到 ${publicPages.length} 个公共主页`, 'success');
            }, 1000);
        }, 1000);
    }



    // 切换到下一个公共主页（优化版 - 一次点击完成）
    function switchToNextPublicPage() {
        // 检查是否有保存的公共主页名称
        if (savedPageNames.length === 0) {
            logUserAction('没有保存的公共主页名称，自动获取公共主页');
            showToast('正在自动获取公共主页...', 'info');

            // 修改获取公共主页的函数调用，使其在获取完成后自动执行切换
            getAllPublicPagesAndSwitch();
            return;
        }

        // 直接执行快速切换，不使用防重复机制
        performFastPageSwitch();
    }

    // 获取所有公共主页并自动切换
    function getAllPublicPagesAndSwitch() {
        logUserAction('开始获取所有公共主页并自动切换');

        // 点击右上角个人主页按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (!profileButton) {
            logUserAction('未找到个人主页按钮');
            isSwitching = false;
            return false;
        }

        profileButton.click();
        logUserAction('已点击个人主页按钮');

        // 等待菜单加载并点击"查看所有主页"
        setTimeout(() => {
            const viewAllPagesButton = document.querySelector('[aria-label="查看所有主页"][role="button"]');
            if (!viewAllPagesButton) {
                logUserAction('未找到"查看所有主页"按钮');
                isSwitching = false;
                return false;
            }

            viewAllPagesButton.click();
            logUserAction('已点击"查看所有主页"按钮');

            // 等待页面列表加载
            setTimeout(() => {
                // 获取所有公共主页
                const pageItems = document.querySelectorAll('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');

                publicPages = [];
                const currentPageNames = [];

                pageItems.forEach((item, index) => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName) {
                        publicPages.push({
                            name: pageName,
                            element: item,
                            index: index
                        });
                        currentPageNames.push(pageName);
                    }
                });

                // 保存公共主页名称到本地存储做标记
                if (currentPageNames.length > 0) {
                    savedPageNames = currentPageNames;
                    localStorage.setItem('fb_saved_page_names', JSON.stringify(savedPageNames));
                    logUserAction(`已保存 ${savedPageNames.length} 个公共主页名称到本地存储`);

                    // 获取完成后，自动执行切换
                    setTimeout(() => {
                        // 关闭菜单
                        document.body.click();

                        // 执行切换
                        performPageSwitchCycle();
                    }, 500);
                } else {
                    // 如果没有找到公共主页，清除切换标志
                    logUserAction('未找到任何公共主页');
                    showToast('未找到任何公共主页', 'error');
                    isSwitching = false;
                    document.body.click(); // 关闭菜单
                }
            }, 1000);
        }, 1000);
    }

    // 快速切换公共主页（优化版 - 一次点击完成，带重试机制）
    function performFastPageSwitch() {
        // 计算当前要切换到的公共主页名称（倒序循环）
        const targetIndex = savedPageNames.length - 1 - currentPageIndex;
        const targetPageName = savedPageNames[targetIndex];

        logUserAction(`🚀 快速切换到: ${targetPageName}`);
        logUserAction(`📍 目标索引: ${targetIndex} (倒序第 ${currentPageIndex + 1} 次点击)`);

        // 使用带重试机制的切换函数
        executePageSwitchWithRetry(targetPageName, 3) // 最多重试3次
            .then(() => {
                logUserAction(`✅ 快速切换完成: ${targetPageName}`);
                showToast(`已切换到: ${targetPageName}`, 'success');
            })
            .catch((error) => {
                logUserAction(`❌ 快速切换最终失败: ${error}`);
                showToast(`切换失败: ${error}`, 'error');
            });
    }

    // 带重试机制的页面切换函数
    function executePageSwitchWithRetry(targetPageName, maxRetries = 3) {
        return new Promise(async (resolve, reject) => {
            let lastError = null;

            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    logUserAction(`🔄 第 ${attempt} 次尝试切换到: ${targetPageName}`);

                    // 如果不是第一次尝试，先关闭可能打开的菜单
                    if (attempt > 1) {
                        logUserAction('🔧 关闭可能打开的菜单...');
                        document.body.click();
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }

                    await executePageSwitch(targetPageName);
                    logUserAction(`✅ 第 ${attempt} 次尝试成功！`);
                    resolve();
                    return;
                } catch (error) {
                    lastError = error;
                    logUserAction(`❌ 第 ${attempt} 次尝试失败: ${error}`);

                    if (attempt < maxRetries) {
                        const waitTime = attempt * 500; // 递增等待时间
                        logUserAction(`⏳ 等待 ${waitTime}ms 后重试...`);
                        await new Promise(resolve => setTimeout(resolve, waitTime));
                    }
                }
            }

            reject(`所有 ${maxRetries} 次尝试都失败了。最后错误: ${lastError}`);
        });
    }

    /**
     * 等待元素出现的辅助函数
     * 这是一键切换功能的重要辅助函数
     * 
     * 功能：
     * 1. 等待指定选择器的元素出现在页面上
     * 2. 如果元素在超时时间内出现，返回该元素
     * 3. 如果超时仍未出现，抛出错误
     * 
     * @param {string} selector - CSS选择器
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<Element>} - 返回找到的元素
     */
    function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function checkElement() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) { // 检查元素是否可见
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(`等待元素超时: ${selector}`);
                    return;
                }

                setTimeout(checkElement, 50); // 每50ms检查一次
            }

            checkElement();
        });
    }

    /**
     * 等待元素列表出现的辅助函数
     * 这是一键切换功能的重要辅助函数
     * 
     * 功能：
     * 1. 等待指定选择器的多个元素出现在页面上
     * 2. 确保找到的元素数量达到最小要求
     * 3. 用于获取公共主页列表时等待所有页面选项加载
     * 
     * @param {string} selector - CSS选择器
     * @param {number} minCount - 最小需要的元素数量
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<NodeList>} - 返回找到的元素列表
     */
    function waitForElements(selector, minCount = 1, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function checkElements() {
                const elements = document.querySelectorAll(selector);
                if (elements.length >= minCount) {
                    resolve(elements);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(`等待元素列表超时: ${selector} (需要至少 ${minCount} 个)`);
                    return;
                }

                setTimeout(checkElements, 50); // 每50ms检查一次
            }

            checkElements();
        });
    }

    // 验证公共主页切换是否成功
    function verifySwitchSuccess(targetPageName, timeout = 3000) {
        return new Promise((resolve) => {
            const startTime = Date.now();

            function checkSwitch() {
                // 检查是否已经切换成功的几种方式：

                // 1. 检查URL是否包含公共主页的标识
                const currentUrl = window.location.href;
                if (currentUrl.includes('/pages/') || currentUrl.includes('/profile.php')) {
                    logUserAction(`✅ URL验证成功: ${currentUrl}`);
                    resolve(true);
                    return;
                }

                // 2. 检查页面标题是否包含公共主页名称
                const pageTitle = document.title;
                if (pageTitle.includes(targetPageName)) {
                    logUserAction(`✅ 页面标题验证成功: ${pageTitle}`);
                    resolve(true);
                    return;
                }

                // 3. 检查是否存在公共主页特有的元素
                const pageIndicators = [
                    '[data-pagelet="ProfileTilesFeed"]',
                    '[data-pagelet="ProfilePlusLikeButton"]',
                    '[role="main"] h1',
                    '[data-pagelet="ProfileActions"]'
                ];

                for (const selector of pageIndicators) {
                    const element = document.querySelector(selector);
                    if (element) {
                        logUserAction(`✅ 页面元素验证成功: 找到 ${selector}`);
                        resolve(true);
                        return;
                    }
                }

                // 4. 检查菜单是否已关闭（说明切换操作完成）
                const menuElements = document.querySelectorAll('[aria-label="查看所有主页"][role="button"]');
                if (menuElements.length === 0) {
                    logUserAction(`✅ 菜单关闭验证成功`);
                    resolve(true);
                    return;
                }

                // 如果超时，返回false但不抛出错误
                if (Date.now() - startTime > timeout) {
                    logUserAction(`⚠️ 切换验证超时，但继续执行`);
                    resolve(true); // 即使验证超时也认为成功，避免阻塞
                    return;
                }

                setTimeout(checkSwitch, 100); // 每100ms检查一次
            }

            checkSwitch();
        });
    }

    // 执行页面切换的Promise函数（优化版 - 真正的一次点击完成）
    function executePageSwitch(targetPageName) {
        return new Promise(async (resolve, reject) => {
            try {
                logUserAction(`🚀 开始执行快速切换到: ${targetPageName}`);

                // 第一步：等待并点击个人主页按钮
                logUserAction('⏳ 等待个人主页按钮...');
                const profileButton = await waitForElement('[aria-label="你的个人主页"][role="button"]');
                profileButton.click();
                logUserAction('✅ 已点击个人主页按钮');

                // 第二步：等待并点击"查看所有主页"按钮
                logUserAction('⏳ 等待"查看所有主页"按钮...');
                const viewAllPagesButton = await waitForElement('[aria-label="查看所有主页"][role="button"]');
                viewAllPagesButton.click();
                logUserAction('✅ 已点击"查看所有主页"按钮');

                // 第三步：等待公共主页列表加载并查找目标主页
                logUserAction('⏳ 等待公共主页列表加载...');
                const pageItems = await waitForElements('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');

                logUserAction(`📋 找到 ${pageItems.length} 个公共主页元素`);

                // 查找目标公共主页
                let targetPage = null;
                pageItems.forEach(item => {
                    const pageName = item.getAttribute('aria-label');
                    logUserAction(`🔍 检查公共主页: ${pageName}`);
                    if (pageName === targetPageName) {
                        targetPage = item;
                        logUserAction(`🎯 找到目标公共主页: ${pageName}`);
                    }
                });

                if (targetPage) {
                    // 确保元素可见并可点击
                    targetPage.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // 稍等一下确保滚动完成
                    await new Promise(resolve => setTimeout(resolve, 100));

                    targetPage.click();
                    logUserAction(`✅ 成功点击公共主页: ${targetPageName}`);

                    // 验证切换是否成功
                    const switchSuccess = await verifySwitchSuccess(targetPageName);

                    if (switchSuccess) {
                        // 立即更新索引
                        const nextIndex = (currentPageIndex + 1) % savedPageNames.length;
                        const nextTargetIndex = savedPageNames.length - 1 - nextIndex;
                        const nextPageName = savedPageNames[nextTargetIndex];

                        currentPageIndex = nextIndex;
                        localStorage.setItem('fb_current_page_index', currentPageIndex.toString());

                        logUserAction(`🔄 下次将切换到: ${nextPageName}`);
                        logUserAction(`🎉 公共主页切换验证成功: ${targetPageName}`);
                        resolve();
                    } else {
                        reject(`切换验证失败: 未能成功切换到 ${targetPageName}`);
                    }
                } else {
                    const availablePages = Array.from(pageItems).map(item => item.getAttribute('aria-label')).join(', ');
                    reject(`未找到公共主页: ${targetPageName}。可用的公共主页: ${availablePages}`);
                }
            } catch (error) {
                logUserAction(`❌ 切换过程中发生错误: ${error}`);
                reject(error);
            }
        });
    }

    // 执行公共主页循环切换（倒序循环）- 保留原版本用于调试
    function performPageSwitchCycle() {
        // 设置切换标志
        isSwitching = true;

        // 检查是否有保存的公共主页名称
        if (savedPageNames.length === 0) {
            logUserAction('没有保存的公共主页名称，请先点击"获取公共主页"');
            showToast('请先获取公共主页', 'error');
            isSwitching = false;
            return;
        }

        // 计算当前要切换到的公共主页名称（倒序循环）
        const targetIndex = savedPageNames.length - 1 - currentPageIndex;
        const targetPageName = savedPageNames[targetIndex];

        // 详细的调试信息
        logUserAction(`🔍 倒序切换调试信息:`);
        logUserAction(`📊 保存的公共主页总数: ${savedPageNames.length}`);
        logUserAction(`🔢 当前循环索引: ${currentPageIndex}`);
        logUserAction(`🎯 计算的目标索引: ${targetIndex}`);
        logUserAction(`📝 目标公共主页名称: ${targetPageName}`);
        logUserAction(`📋 公共主页列表: [${savedPageNames.join(', ')}]`);

        // 添加倒序切换顺序的完整展示
        logUserAction(`🔄 完整倒序切换顺序:`);
        for (let i = 0; i < savedPageNames.length; i++) {
            const reverseIndex = savedPageNames.length - 1 - i;
            const pageName = savedPageNames[reverseIndex];
            const marker = (i === currentPageIndex) ? ' ← 当前' : '';
            logUserAction(`   第${i + 1}次点击 → ${pageName} (原索引: ${reverseIndex})${marker}`);
        }

        // 点击右上角个人主页按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (!profileButton) {
            logUserAction('未找到个人主页按钮');
            isSwitching = false;
            return;
        }

        profileButton.click();
        logUserAction('已点击个人主页按钮');

        // 等待菜单加载并点击"查看所有主页"
        setTimeout(() => {
            const viewAllPagesButton = document.querySelector('[aria-label="查看所有主页"][role="button"]');
            if (!viewAllPagesButton) {
                logUserAction('未找到"查看所有主页"按钮');
                isSwitching = false;
                return;
            }

            viewAllPagesButton.click();
            logUserAction('已点击"查看所有主页"按钮');

            // 等待页面列表加载
            setTimeout(() => {
                // 直接使用指定的选择器路径查找所有公共主页
                const pageItems = document.querySelectorAll('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');

                if (pageItems.length === 0) {
                    logUserAction('未找到任何公共主页元素');
                    isSwitching = false;
                    return;
                }

                // 根据保存的名称查找目标公共主页
                let targetPage = null;
                pageItems.forEach(item => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName === targetPageName) {
                        targetPage = item;
                    }
                });

                if (targetPage) {
                    targetPage.click();
                    logUserAction(`✅ 成功切换到公共主页: ${targetPageName}`);
                    logUserAction(`📍 当前位置: 第 ${targetIndex + 1} 个公共主页 (倒序第 ${currentPageIndex + 1} 次点击)`);

                    // 更新索引，准备下一次切换（倒序循环）
                    const nextIndex = (currentPageIndex + 1) % savedPageNames.length;
                    const nextTargetIndex = savedPageNames.length - 1 - nextIndex;
                    const nextPageName = savedPageNames[nextTargetIndex];

                    currentPageIndex = nextIndex;
                    localStorage.setItem('fb_current_page_index', currentPageIndex.toString());

                    logUserAction(`🔄 下次将切换到: ${nextPageName} (第 ${nextTargetIndex + 1} 个公共主页)`);
                    showToast(`已切换到: ${targetPageName}`, 'success');

                    // 清除切换标志
                    isSwitching = false;
                } else {
                    logUserAction(`❌ 未找到公共主页: ${targetPageName}`);
                    showToast(`未找到公共主页: ${targetPageName}`, 'error');

                    // 清除切换标志
                    isSwitching = false;
                }
            }, 1000);
        }, 1000);
    }



    // 重置公共主页切换索引
    function resetPageIndex() {
        currentPageIndex = 0;
        localStorage.setItem('fb_current_page_index', '0');
        logUserAction('已重置公共主页切换索引为 0');
        showToast('已重置切换索引', 'success');
    }

    // 清除保存的公共主页名称
    function clearSavedPageNames() {
        savedPageNames = [];
        localStorage.removeItem('fb_saved_page_names');
        currentPageIndex = 0;
        localStorage.setItem('fb_current_page_index', '0');
        logUserAction('已清除保存的公共主页名称和索引');
        showToast('已清除公共主页标记', 'success');
    }

    // 测试倒序切换逻辑（不实际切换）
    function testReverseLogic() {
        if (savedPageNames.length === 0) {
            logUserAction('❌ 没有保存的公共主页名称，请先获取公共主页');
            showToast('请先获取公共主页', 'error');
            return;
        }

        logUserAction(`🧪 测试倒序切换逻辑:`);
        logUserAction(`📋 公共主页列表: [${savedPageNames.join(', ')}]`);
        logUserAction(`🔢 当前索引: ${currentPageIndex}`);

        // 模拟连续5次点击的结果
        logUserAction(`🔄 模拟连续点击结果:`);
        for (let i = 0; i < Math.min(5, savedPageNames.length * 2); i++) {
            const testIndex = (currentPageIndex + i) % savedPageNames.length;
            const targetIndex = savedPageNames.length - 1 - testIndex;
            const targetPageName = savedPageNames[targetIndex];
            const marker = (i === 0) ? ' ← 下次点击' : '';
            logUserAction(`   第${i + 1}次点击 → ${targetPageName} (循环索引: ${testIndex}, 目标索引: ${targetIndex})${marker}`);
        }

        showToast('已输出测试结果到日志', 'info');
    }

    // ===== 一键切换功能核心代码开始 =====
    
    /**
     * 超级快速切换（一键完成，无需多次点击）- 优化版
     * 这是一键切换功能的主入口函数
     * 功能：
     * 1. 检查是否有保存的公共主页列表
     * 2. 如果没有，自动获取公共主页列表
     * 3. 执行快速切换操作
     * 4. 处理切换结果并显示提示
     */
    function performSuperFastSwitch() {
        if (savedPageNames.length === 0) {
            logUserAction('没有保存的公共主页名称，自动获取公共主页');
            showToast('正在自动获取公共主页...', 'info');

            // 先获取公共主页，然后自动执行切换
            getAllPublicPagesAndThenSwitch();
            return;
        }

        const targetIndex = savedPageNames.length - 1 - currentPageIndex;
        const targetPageName = savedPageNames[targetIndex];

        logUserAction(`⚡ 超级快速切换到: ${targetPageName}`);
        showToast(`正在切换到: ${targetPageName}`, 'info');

        // 使用最优化的切换策略
        executeSuperFastSwitch(targetPageName)
            .then(() => {
                logUserAction(`🎉 超级快速切换成功: ${targetPageName}`);
                showToast(`✅ 已切换到: ${targetPageName}`, 'success');
            })
            .catch((error) => {
                logUserAction(`❌ 超级快速切换失败: ${error}`);
                showToast(`❌ 切换失败: ${error}`, 'error');

                // 失败后自动重试一次
                setTimeout(() => {
                    logUserAction(`🔄 自动重试切换到: ${targetPageName}`);
                    executeSuperFastSwitch(targetPageName, true)
                        .then(() => {
                            logUserAction(`🎉 重试切换成功: ${targetPageName}`);
                            showToast(`✅ 已切换到: ${targetPageName}`, 'success');
                        })
                        .catch((retryError) => {
                            logUserAction(`❌ 重试切换失败: ${retryError}`);
                            showToast(`❌ 切换失败，请手动切换`, 'error');
                        });
                }, 500);
            });
    }

    // 获取所有公共主页然后执行超级快速切换 - 优化版
    function getAllPublicPagesAndThenSwitch() {
        logUserAction('开始获取所有公共主页并执行超级快速切换');

        // 使用MutationObserver监听DOM变化，更可靠地检测元素
        const observer = new MutationObserver((mutations, obs) => {
            // 尝试查找个人主页按钮
            const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
            if (profileButton) {
                obs.disconnect(); // 停止观察
                executeGetPagesSequence(profileButton);
            }
        });

        // 开始观察文档变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 同时尝试直接查找按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (profileButton) {
            observer.disconnect(); // 停止观察
            executeGetPagesSequence(profileButton);
        }

        // 设置超时，防止无限等待
        setTimeout(() => {
            observer.disconnect();
            logUserAction('获取公共主页超时，请手动尝试');
            showToast('获取公共主页超时，请刷新页面重试', 'error');
        }, 10000);
    }

    // 执行获取页面的完整序列
    function executeGetPagesSequence(profileButton) {
        // 点击个人主页按钮
        profileButton.click();
        logUserAction('已点击个人主页按钮');

        // 使用Promise和setTimeout组合，更可靠地处理异步操作
        waitForElement('[aria-label="查看所有主页"][role="button"]', 2000)
            .then(viewAllPagesButton => {
                viewAllPagesButton.click();
                logUserAction('已点击"查看所有主页"按钮');

                // 等待页面列表加载
                return waitForElements('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]', 1, 3000);
            })
            .then(pageItems => {
                publicPages = [];
                const currentPageNames = [];

                pageItems.forEach((item, index) => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName) {
                        publicPages.push({
                            name: pageName,
                            element: item,
                            index: index
                        });
                        currentPageNames.push(pageName);
                    }
                });

                // 保存公共主页名称到本地存储做标记
                if (currentPageNames.length > 0) {
                    savedPageNames = currentPageNames;
                    localStorage.setItem('fb_saved_page_names', JSON.stringify(savedPageNames));
                    logUserAction(`已保存 ${savedPageNames.length} 个公共主页名称到本地存储`);

                    // 关闭菜单
                    document.body.click();

                    // 重置索引并执行切换
                    currentPageIndex = 0;
                    localStorage.setItem('fb_current_page_index', '0');

                    // 延迟一点执行切换，确保菜单已关闭
                    setTimeout(() => {
                        performSuperFastSwitch();
                    }, 300);
                } else {
                    logUserAction('未找到任何公共主页');
                    showToast('未找到任何公共主页', 'error');
                    document.body.click(); // 关闭菜单
                }
            })
            .catch(error => {
                logUserAction(`获取公共主页过程中出错: ${error}`);
                showToast('获取公共主页失败，请重试', 'error');
                document.body.click(); // 尝试关闭可能打开的菜单
            });
    }

    /**
     * 执行超级快速切换的核心函数 - 优化版
     * 这是实际执行页面切换的核心实现
     * 
     * 工作流程：
     * 1. 点击个人主页按钮打开菜单
     * 2. 点击"查看所有主页"展开列表
     * 3. 在列表中查找并点击目标页面
     * 4. 验证切换是否成功
     * 5. 更新切换索引为下一个目标
     * 
     * @param {string} targetPageName - 目标公共主页名称
     * @param {boolean} isRetry - 是否为重试操作
     * @returns {Promise} - 返回切换操作的Promise
     */
    function executeSuperFastSwitch(targetPageName, isRetry = false) {
        return new Promise(async (resolve, reject) => {
            try {
                logUserAction(`🚀 开始${isRetry ? '重试' : ''}超级快速切换流程`);

                // 使用直接选择器组合，提高查找效率
                const selectors = {
                    profileButton: '[aria-label="你的个人主页"][role="button"]',
                    viewAllPages: '[aria-label="查看所有主页"][role="button"]',
                    pageItems: '[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]'
                };

                // 第一步：直接点击个人主页按钮，不等待滚动完成
                const profileButton = document.querySelector(selectors.profileButton);
                if (!profileButton) {
                    throw new Error('未找到个人主页按钮');
                }

                // 使用直接点击，减少延迟
                profileButton.click();
                logUserAction('已点击个人主页按钮');

                // 使用更短的等待时间
                await new Promise(resolve => setTimeout(resolve, 200));

                // 第二步：使用waitForElement等待"查看所有主页"按钮出现并点击
                let viewAllPagesButton;
                try {
                    viewAllPagesButton = await waitForElement(selectors.viewAllPages, 1500);
                    viewAllPagesButton.click();
                    logUserAction('已点击"查看所有主页"按钮');
                } catch (error) {
                    // 如果超时，尝试重新点击个人主页按钮
                    logUserAction('未找到"查看所有主页"按钮，尝试重新点击个人主页按钮');
                    document.body.click(); // 先关闭可能已打开的菜单
                    await new Promise(resolve => setTimeout(resolve, 200));
                    profileButton.click();

                    // 再次等待按钮出现
                    try {
                        viewAllPagesButton = await waitForElement(selectors.viewAllPages, 1500);
                        viewAllPagesButton.click();
                        logUserAction('第二次尝试成功点击"查看所有主页"按钮');
                    } catch (retryError) {
                        throw new Error('无法找到"查看所有主页"按钮，请检查Facebook界面是否有变化');
                    }
                }

                // 第三步：等待页面列表加载，使用更短的超时时间
                let pageItems;
                try {
                    pageItems = await waitForElements(selectors.pageItems, 1, 2000);
                } catch (error) {
                    throw new Error('加载公共主页列表超时，Facebook可能加载缓慢');
                }

                // 记录找到的所有页面名称，用于调试
                const foundPageNames = Array.from(pageItems).map(item => item.getAttribute('aria-label'));
                logUserAction(`找到 ${pageItems.length} 个公共主页: ${foundPageNames.join(', ')}`);

                // 查找目标公共主页并立即点击，不等待滚动完成
                let targetFound = false;

                for (const item of pageItems) {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName === targetPageName) {
                        // 使用更可靠的点击方法组合
                        try {
                            // 1. 直接点击
                            item.click();
                            logUserAction(`已点击目标公共主页: ${targetPageName}`);
                            targetFound = true;
                        } catch (e) {
                            logUserAction(`直接点击失败，尝试替代方法: ${e.message}`);

                            try {
                                // 2. 使用MouseEvent
                                const clickEvent = new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                });
                                item.dispatchEvent(clickEvent);
                                targetFound = true;
                                logUserAction(`使用MouseEvent点击成功`);
                            } catch (e2) {
                                // 3. 最后尝试使用模拟点击
                                try {
                                    const rect = item.getBoundingClientRect();
                                    const centerX = rect.left + rect.width / 2;
                                    const centerY = rect.top + rect.height / 2;

                                    // 创建并分发鼠标事件
                                    ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                                        const event = new MouseEvent(eventType, {
                                            view: window,
                                            bubbles: true,
                                            cancelable: true,
                                            clientX: centerX,
                                            clientY: centerY
                                        });
                                        item.dispatchEvent(event);
                                    });

                                    targetFound = true;
                                    logUserAction(`使用模拟点击成功`);
                                } catch (e3) {
                                    logUserAction(`所有点击方法都失败: ${e3.message}`);
                                }
                            }
                        }

                        if (targetFound) {
                            break;
                        }
                    }
                }

                if (!targetFound) {
                    throw new Error(`未找到目标公共主页: ${targetPageName}`);
                }

                // 等待页面切换完成，使用更短的等待时间
                await new Promise(resolve => setTimeout(resolve, 500));

                // 更新索引
                const nextIndex = (currentPageIndex + 1) % savedPageNames.length;
                currentPageIndex = nextIndex;
                localStorage.setItem('fb_current_page_index', currentPageIndex.toString());

                logUserAction(`✅ 超级快速切换完成，索引已更新为: ${nextIndex}`);
                resolve();

            } catch (error) {
                logUserAction(`❌ 切换过程中出错: ${error.message}`);
                reject(error);
            }
        });
    }

    // 添加公共主页管理按钮到控制面板
    function addPageManagementButtons(buttonGroup) {
        const superFastButton = createButton('一键切换', '#ff6b35', '#e55a2b');
        superFastButton.onclick = performSuperFastSwitch; // 使用超级快速切换
        buttonGroup.appendChild(superFastButton);
    }



    // 添加控制面板
    function addControlPanel() {
        // 主面板
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 320px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
        `;

        // 标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        `;

        const title = document.createElement('div');
        title.textContent = 'Facebook 自动点赞';
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            color: #1877f2;
        `;

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.innerHTML = '−';
        minimizeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 0 5px;
            line-height: 1;
        `;

        let isMinimized = false;
        const content = document.createElement('div');
        content.style.transition = 'all 0.3s ease';

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            content.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.innerHTML = isMinimized ? '+' : '−';
            panel.style.width = isMinimized ? 'auto' : '320px';
        };

        titleBar.appendChild(title);
        titleBar.appendChild(minimizeBtn);
        panel.appendChild(titleBar);

        // 创建带标签的区域函数
        function createLabeledSection(labelText) {
            const section = document.createElement('div');
            section.style.marginBottom = '15px';

            const label = document.createElement('label');
            label.textContent = labelText;
            label.style.cssText = `
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
                color: #444;
                font-size: 14px;
            `;

            section.appendChild(label);
            return section;
        }

        // 链接输入区域
        const inputSection = createLabeledSection('批量添加链接');
        const input = document.createElement('textarea');
        input.placeholder = '输入 Facebook 链接（每行一个）';
        input.style.cssText = `
            width: 100%;
            height: 80px;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 14px;
            box-sizing: border-box;
        `;
        inputSection.appendChild(input);

        // 文件上传区域
        const uploadSection = createLabeledSection('文件上传');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.txt';
        fileInput.style.display = 'none';

        const dropZone = document.createElement('div');
        dropZone.style.cssText = `
            border: 2px dashed #1877f2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f0f2f5;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        dropZone.innerHTML = `
            <div style="color: #1877f2; margin-bottom: 5px;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 20px;"></i>
            </div>
            <div style="color: #666; font-size: 13px;">
                点击或拖放文件到这里上传<br>
                <span style="font-size: 12px; color: #999;">支持 .txt 文件</span>
            </div>
        `;

        // 处理文件内容的函数
        function handleFileContent(content) {
            if (content.trim()) {
                if (addToLikeList(content)) {
                    const urls = parseFacebookUrls(content);
                    showToast(`成功从文件添加 ${urls.length} 个链接！`);
                    updatePendingList();
                }
            } else {
                showToast('文件内容为空！', 'error');
            }
        }

        // 处理文件的函数
        function handleFile(file) {
            if (file.type !== 'text/plain') {
                showToast('请上传 .txt 文件！', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                handleFileContent(e.target.result);
            };
            reader.onerror = () => {
                showToast('读取文件失败！', 'error');
            };
            reader.readAsText(file);
        }

        // 点击上传
        dropZone.onclick = () => fileInput.click();

        // 文件选择处理
        fileInput.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
            // 清除选择，这样同一个文件可以重复上传
            fileInput.value = '';
        };

        // 拖拽相关事件
        dropZone.ondragover = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#28a745';
            dropZone.style.background = '#e8f5e9';
        };

        dropZone.ondragleave = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';
        };

        dropZone.ondrop = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';

            const file = e.dataTransfer.files[0];
            if (file) {
                handleFile(file);
            }
        };

        uploadSection.appendChild(dropZone);
        uploadSection.appendChild(fileInput);

        // 待处理链接列表区域
        const pendingSection = createLabeledSection('待处理链接');
        const pendingHeader = document.createElement('div');
        pendingHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        `;

        // 添加清除所有链接的按钮
        const clearAllButton = document.createElement('button');
        clearAllButton.textContent = '清除所有链接';
        clearAllButton.style.cssText = `
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            &:hover {
                background: #dc3545;
                color: white;
            }
        `;
        clearAllButton.onclick = () => {
            if (confirm('确定要清除所有链接吗？此操作不可恢复。')) {
                clearAllPendingLinks();
                updatePendingList();
            }
        };

        pendingHeader.appendChild(clearAllButton);
        pendingSection.appendChild(pendingHeader);

        const pendingList = document.createElement('div');
        pendingList.style.cssText = `
            max-height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
        `;

        // 更新待处理列表的函数
        function updatePendingList() {
            const pendingPosts = getPendingPosts();
            pendingList.innerHTML = '';
            pendingPosts.forEach(url => {
                const item = document.createElement('div');
                item.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px;
                    border-bottom: 1px solid #eee;
                `;

                const urlText = document.createElement('div');
                urlText.style.cssText = `
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 10px;
                `;
                urlText.textContent = url;

                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '×';
                deleteBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #dc3545;
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0 5px;
                `;
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    const index = targetPosts.indexOf(url);
                    if (index > -1) {
                        targetPosts.splice(index, 1);
                        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
                        updatePendingList();
                        logUserAction(`删除链接: ${url}`);
                    }
                };

                item.appendChild(urlText);
                item.appendChild(deleteBtn);
                pendingList.appendChild(item);
            });
        }
        pendingSection.appendChild(pendingList);

        // 操作日志面板
        const logSection = createLabeledSection('操作日志');
        const logPanel = document.createElement('div');
        logPanel.id = 'userLogPanel';
        logPanel.style.cssText = `
            height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
            margin-bottom: 15px;
        `;
        logSection.appendChild(logPanel);

        // 按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.style.cssText = `
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        `;

        function createButton(text, bgColor, hoverColor) {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.cssText = `
                background: ${bgColor};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
                flex: 1;
                min-width: 120px;
                white-space: nowrap;
            `;
            button.onmouseover = () => button.style.background = hoverColor;
            button.onmouseout = () => button.style.background = bgColor;
            return button;
        }

        const startButton = createButton('开始点赞', '#1877f2', '#0d65d9');
        const stopButton = createButton('停止点赞', '#dc3545', '#c82333');
        const clearLogsButton = createButton('清除日志', '#6c757d', '#5a6268');
        const addButton = createButton('添加链接', '#28a745', '#218838');

        startButton.onclick = startAutoLike;
        stopButton.onclick = stopAutoLike;
        clearLogsButton.onclick = () => {
            if (confirm('确定要清除所有日志记录吗？')) {
                clearLogs();
            }
        };
        addButton.onclick = () => {
            if (addToLikeList(input.value)) {
                const urls = parseFacebookUrls(input.value);
                showToast(`成功添加 ${urls.length} 个链接！`);
                input.value = '';
                updatePendingList();
            }
        };

        // 创建公共主页管理按钮组
        const pageButtonGroup = document.createElement('div');
        pageButtonGroup.style.cssText = `
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        `;

        // 添加公共主页管理按钮
        addPageManagementButtons(pageButtonGroup);

        // 添加所有元素到内容区
        content.appendChild(inputSection);
        content.appendChild(uploadSection);
        content.appendChild(pendingSection);
        content.appendChild(logSection);
        buttonGroup.appendChild(startButton);
        buttonGroup.appendChild(stopButton);
        buttonGroup.appendChild(clearLogsButton);
        buttonGroup.appendChild(addButton);
        content.appendChild(buttonGroup);
        content.appendChild(pageButtonGroup);

        panel.appendChild(content);
        document.body.appendChild(panel);

        // 初始化显示
        updatePendingList();
    }

    // 添加提示框功能
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : '#F44336'};
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s';
            setTimeout(() => document.body.removeChild(toast), 500);
        }, 3000);
    }

    // 添加必要的动画样式
    const style1 = document.createElement('style');
    style1.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style1);

    // 页面加载时检查是否需要继续任务
    window.addEventListener('load', function() {
        if (isRunning) {
            const currentUrl = window.location.href;

            if (currentOriginalUrl) {
                // 如果有正在处理的原始URL
                if (!currentFinalUrl) {
                    // 如果还没有记录最终URL，说明这是重定向后的页面
                    logUserAction(`处理重定向后的页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else if (currentUrl === currentFinalUrl) {
                    // 如果当前URL与记录的最终URL匹配
                    logUserAction(`继续处理当前页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else {
                    // URL不匹配，可能是用户手动导航，重新开始处理
                    performLike();
                }
            } else {
                // 没有正在处理的URL，开始处理新的链接
                performLike();
            }
        }
    });

    // 初始化时如果正在运行，则继续执行
    if (isRunning) {
        window.addEventListener('load', function() {
            logUserAction('恢复自动点赞任务');
            const currentUrl = window.location.href;
            checkAndLike(currentUrl);
        });
    }

    // 在页面加载时初始化公共主页索引和保存的名称
    window.addEventListener('load', function() {
        // 恢复当前页面索引
        const savedIndex = localStorage.getItem('fb_current_page_index');
        if (savedIndex !== null) {
            currentPageIndex = parseInt(savedIndex, 10);
        }

        // 恢复保存的公共主页名称
        const savedNames = localStorage.getItem('fb_saved_page_names');
        if (savedNames) {
            savedPageNames = JSON.parse(savedNames);
            logUserAction(`恢复了 ${savedPageNames.length} 个保存的公共主页名称`);
        }
    });

    // 初始化
    addControlPanel();
    console.log('Facebook Auto Liker 已啟動');
})();





















